import { createSupabaseServerClient } from '@/lib/supabase/client'

export interface Chapter {
  title: string
  content: string
  chapter_number: number
  word_count: number
}

export interface ProcessingResult {
  chapters: Chapter[]
  wordCount: number
  readingTimeMinutes: number
  pageCount: number
  metadata?: {
    title?: string
    author?: string
    description?: string
  }
}

/**
 * Main entry point for processing ebook files from URL
 */
export async function processEbook(projectId: string, fileUrl: string, fileType: 'epub' | 'docx' | 'doc' | 'rtf'): Promise<ProcessingResult> {
  try {
    console.log(`Processing ${fileType.toUpperCase()} from URL: ${fileUrl}`)

    // Download the file
    const response = await fetch(fileUrl)
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`)
    }

    const buffer = await response.arrayBuffer()
    console.log('Buffer size:', buffer.byteLength)

    if (fileType === 'epub') {
      return await processEPUB(Buffer.from(buffer), false) // Full processing
    } else {
      // Document files (DOCX, DOC, RTF) - convert to EPUB first
      return await processDocumentToEPUB(Buffer.from(buffer), fileType, false)
    }
  } catch (error) {
    console.error('Error processing ebook:', error)
    throw error
  }
}

/**
 * Process ebook buffer directly
 */
export async function processEbookBuffer(buffer: Buffer, fileType: 'epub' | 'docx' | 'doc' | 'rtf', previewOnly: boolean = false): Promise<ProcessingResult> {
  console.log(`Processing ${fileType.toUpperCase()} buffer${previewOnly ? ' (PREVIEW MODE - Chapter 1 only)' : ''}`)

  if (fileType === 'epub') {
    return await processEPUB(buffer, previewOnly)
  } else {
    // Document files - convert to EPUB first
    return await processDocumentToEPUB(buffer, fileType, previewOnly)
  }
}

/**
 * Process EPUB files using epub2 library
 */
async function processEPUB(buffer: Buffer, previewOnly: boolean = false): Promise<ProcessingResult> {
  try {
    const { WordTokenizer } = await import('natural')
    const readingTime = (await import('reading-time')).default
    const fs = await import('fs')
    const path = await import('path')
    const os = await import('os')
    const EPub = (await import('epub2')).EPub

    const wordTokenizer = new WordTokenizer()

    console.log('Processing EPUB buffer, size:', buffer.length)

    // Create temporary file since epub2 requires file path
    const tempDir = os.tmpdir()
    const tempFilePath = path.join(tempDir, `epub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.epub`)

    try {
      // Write buffer to temporary file
      await fs.promises.writeFile(tempFilePath, buffer)
      console.log('Created temporary EPUB file:', tempFilePath)

      // Verify the file was written correctly
      const fileStats = await fs.promises.stat(tempFilePath)
      console.log('Temporary file stats:', { size: fileStats.size, exists: true })

      // Parse EPUB using createAsync
      const epub = await EPub.createAsync(tempFilePath)
      console.log('EPUB parsed successfully')

      // Debug: Log the entire epub object structure
      console.log('EPUB object keys:', Object.keys(epub))
      console.log('EPUB metadata keys:', Object.keys(epub.metadata || {}))
      console.log('EPUB flow type:', typeof epub.flow, 'length:', epub.flow?.length)
      console.log('EPUB spine type:', typeof epub.spine, 'length:', epub.spine?.length)
      console.log('EPUB manifest type:', typeof epub.manifest, 'keys:', Object.keys(epub.manifest || {}))

      // Extract metadata
      const metadata = {
        title: epub.metadata.title || 'Untitled Book',
        author: epub.metadata.creator || 'Unknown Author',
        description: epub.metadata.description || ''
      }

      console.log('EPUB metadata:', metadata)
      console.log('EPUB processing continuing without event listener...')

    // Get chapters from spine
    const spineItems = epub.flow
    console.log(`Found ${spineItems.length} spine items`)
    console.log('Spine items:', spineItems.map(item => ({ id: item.id, href: item.href, title: item.title })))

    const chapters: Chapter[] = []
    let totalWordCount = 0

    // Process chapters (limit to first actual chapter if preview mode)
    let itemsToProcess = spineItems
    if (previewOnly) {
      // For preview, try to find the first actual chapter (skip title page, contents, etc.)
      const firstChapterIndex = spineItems.findIndex(item =>
        item.id.includes('chapter-') ||
        item.title?.toLowerCase().includes('chapter') ||
        item.href?.includes('chapter-')
      )
      if (firstChapterIndex >= 0) {
        itemsToProcess = spineItems.slice(firstChapterIndex, firstChapterIndex + 1)
        console.log(`Preview mode: Found first chapter at index ${firstChapterIndex}: ${spineItems[firstChapterIndex].id}`)
      } else {
        // Fallback to first item if no chapter found
        itemsToProcess = spineItems.slice(0, 1)
        console.log('Preview mode: No chapter found, using first item')
      }
    }

    for (let i = 0; i < itemsToProcess.length; i++) {
      const item = itemsToProcess[i]

      try {
        console.log(`Processing chapter ${i + 1}: ${item.id}`)

        // Get chapter content - try different methods
        let content = ''
        let chapterData

        try {
          // Use callback-based API for epub2
          chapterData = await new Promise((resolve, reject) => {
            epub.getChapter(item.id, (error, data) => {
              if (error) {
                reject(error)
              } else {
                resolve(data)
              }
            })
          })

          console.log('Chapter data type:', typeof chapterData, 'length:', chapterData?.length)

          if (Buffer.isBuffer(chapterData)) {
            content = chapterData.toString('utf-8')
          } else if (typeof chapterData === 'string') {
            content = chapterData
          } else {
            console.log('Unexpected chapter data format:', chapterData)
            continue
          }
        } catch (chapterError) {
          console.error(`Failed to get chapter ${item.id}:`, chapterError)
          // Try alternative method if available
          try {
            chapterData = await new Promise((resolve, reject) => {
              epub.getChapterRaw(item.id, (error, data) => {
                if (error) {
                  reject(error)
                } else {
                  resolve(data)
                }
              })
            })

            if (Buffer.isBuffer(chapterData)) {
              content = chapterData.toString('utf-8')
            } else if (typeof chapterData === 'string') {
              content = chapterData
            }
          } catch (rawError) {
            console.error(`Failed to get raw chapter ${item.id}:`, rawError)
            continue
          }
        }

        console.log(`Raw content length: ${content.length}`)

        // Clean up HTML content
        content = cleanHTMLContent(content)
        console.log(`Cleaned content length: ${content.length}`)

        // Be more lenient with content length - some chapters might be short
        if (content.trim().length < 10) {
          console.log(`Skipping very short chapter: ${content.length} characters`)
          continue
        }

        // Extract title from content or use default
        let title = extractTitleFromContent(content) || item.title || `Chapter ${chapters.length + 1}`

        // Count words
        const words = wordTokenizer.tokenize(content.toLowerCase()) || []
        const wordCount = words.length

        totalWordCount += wordCount

        chapters.push({
          title,
          content,
          chapter_number: chapters.length + 1,
          word_count: wordCount
        })

        console.log(`Chapter "${title}": ${wordCount} words`)

      } catch (chapterError) {
        console.error(`Error processing chapter ${i + 1}:`, chapterError)
        continue
      }
    }

    if (chapters.length === 0) {
      console.log('No chapters found through spine, trying alternative methods...')

      // Try to get content from manifest or other sources
      try {
        console.log('Available manifest items:', Object.keys(epub.manifest || {}))
        console.log('Available spine items:', epub.spine?.length || 0)
        console.log('Available flow items:', epub.flow?.length || 0)

        // Try to extract any text content we can find
        const manifestItems = Object.values(epub.manifest || {})
        for (const item of manifestItems) {
          if (item['media-type'] === 'application/xhtml+xml' || item.href?.endsWith('.html') || item.href?.endsWith('.xhtml')) {
            try {
              console.log(`Trying manifest item: ${item.id} (${item.href})`)
              const itemContent = await new Promise((resolve, reject) => {
                epub.getChapter(item.id, (error, data) => {
                  if (error) {
                    reject(error)
                  } else {
                    resolve(data)
                  }
                })
              })

              if (itemContent && itemContent.length > 0) {
                let content = Buffer.isBuffer(itemContent) ? itemContent.toString('utf-8') : itemContent
                content = cleanHTMLContent(content)

                if (content.trim().length > 10) {
                  const words = wordTokenizer.tokenize(content.toLowerCase()) || []
                  chapters.push({
                    title: item.title || `Chapter ${chapters.length + 1}`,
                    content,
                    chapter_number: chapters.length + 1,
                    word_count: words.length
                  })
                  totalWordCount += words.length
                  console.log(`Added chapter from manifest: ${words.length} words`)
                }
              }
            } catch (manifestError) {
              console.log(`Failed to get content from manifest item ${item.id}:`, manifestError.message)
            }
          }
        }
      } catch (manifestError) {
        console.error('Error processing manifest:', manifestError)
      }

      if (chapters.length === 0) {
        throw new Error('No valid chapters found in EPUB. This may be due to an unsupported EPUB structure from Vellum or other authoring software.')
      }
    }

    // Calculate reading time and page count
    const readingTimeResult = readingTime(chapters.map(ch => ch.content).join(' '))
    const pageCount = Math.ceil(totalWordCount / 250) // ~250 words per page

    console.log(`Processing complete: ${chapters.length} chapters, ${totalWordCount} words, ${readingTimeResult.minutes} min read`)

      return {
        chapters,
        wordCount: totalWordCount,
        readingTimeMinutes: Math.round(readingTimeResult.minutes), // Round to integer
        pageCount,
        metadata
      }

    } finally {
      // Clean up temporary file
      try {
        await fs.promises.unlink(tempFilePath)
        console.log('Cleaned up temporary file:', tempFilePath)
      } catch (cleanupError) {
        console.warn('Failed to clean up temporary file:', cleanupError)
      }
    }

  } catch (error) {
    console.error('EPUB processing error:', error)
    throw new Error(`EPUB processing failed: ${error.message}`)
  }
}

/**
 * Clean HTML content for display
 */
function cleanHTMLContent(html: string): string {
  // Remove DOCTYPE, html, head, body tags but keep content structure
  let cleaned = html
    .replace(/<!DOCTYPE[^>]*>/gi, '')
    .replace(/<\?xml[^>]*>/gi, '')
    .replace(/<html[^>]*>/gi, '')
    .replace(/<\/html>/gi, '')
    .replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '')
    .replace(/<body[^>]*>/gi, '')
    .replace(/<\/body>/gi, '')

  // Clean up whitespace
  cleaned = cleaned
    .replace(/\s+/g, ' ')
    .replace(/>\s+</g, '><')
    .trim()

  return cleaned
}

/**
 * Extract title from chapter content
 */
function extractTitleFromContent(content: string): string | null {
  // Try to find h1, h2, or first strong/b tag
  const titlePatterns = [
    /<h[1-2][^>]*>(.*?)<\/h[1-2]>/i,
    /<strong[^>]*>(.*?)<\/strong>/i,
    /<b[^>]*>(.*?)<\/b>/i
  ]

  for (const pattern of titlePatterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      let title = match[1]
        .replace(/<[^>]*>/g, '') // Remove any nested HTML
        .trim()
      
      if (title.length > 0 && title.length <= 100) {
        return title
      }
    }
  }

  return null
}

/**
 * Check if content is likely a table of contents
 */
function isTableOfContents(content: string): boolean {
  const tocIndicators = [
    /table\s+of\s+contents/i,
    /contents/i,
    /chapter\s+\d+.*chapter\s+\d+/i, // Multiple "Chapter X" entries
    /<a[^>]*href[^>]*>.*chapter/i // Links to chapters
  ]

  const cleanContent = content.replace(/<[^>]*>/g, ' ').toLowerCase()
  
  return tocIndicators.some(pattern => pattern.test(cleanContent)) &&
         cleanContent.length < 2000 // TOCs are usually short
}

/**
 * Process document files (DOCX, DOC, RTF) by converting to EPUB format
 */
async function processDocumentToEPUB(buffer: Buffer, fileType: 'docx' | 'doc' | 'rtf', previewOnly: boolean = false, onProgress?: (progress: number, message: string) => void): Promise<ProcessingResult> {
  try {
    console.log(`Processing ${fileType.toUpperCase()} document${previewOnly ? ' (PREVIEW MODE)' : ''}`)
    onProgress?.(10, 'Initializing document processing...')

    let extractedText = ''
    let extractedHtml = ''

    if (fileType === 'docx' || fileType === 'doc') {
      // Use mammoth for Word documents with enhanced formatting
      console.log('Processing Word document with mammoth...')
      onProgress?.(20, 'Extracting text from Word document...')
      const mammoth = await import('mammoth')

      // Enhanced mammoth options for better formatting
      const options = {
        styleMap: [
          // Map Word styles to proper HTML
          "p[style-name='Heading 1'] => h1:fresh",
          "p[style-name='Heading 2'] => h2:fresh",
          "p[style-name='Heading 3'] => h3:fresh",
          "p[style-name='Title'] => h1.title:fresh",
          "p[style-name='Subtitle'] => h2.subtitle:fresh",
          // Handle bold text that might be chapter headers
          "b => strong",
          "i => em",
          // Center alignment for titles
          "p[style-name='centered'] => p.text-center:fresh"
        ],
        includeDefaultStyleMap: true
      }

      const result = await mammoth.convertToHtml({ buffer }, options)
      let rawHtml = result.value

      // Apply intelligent formatting enhancements
      onProgress?.(50, 'Enhancing document formatting...')
      extractedHtml = enhanceDocumentFormatting(rawHtml)

      // Also extract plain text for word counting
      const textResult = await mammoth.extractRawText({ buffer })
      extractedText = textResult.value

      console.log('Mammoth extraction completed')
      console.log('Warnings:', result.messages)

    } else if (fileType === 'rtf') {
      // Use rtf-parser for RTF documents
      console.log('Processing RTF document...')
      const { parseRtf } = await import('rtf-parser')

      const rtfString = buffer.toString('utf8')
      console.log('RTF string length:', rtfString.length)

      const doc = parseRtf(rtfString)
      console.log('RTF parsed document:', doc)

      // Extract text content from RTF structure
      extractedText = extractTextFromRtfDoc(doc)
      let basicHtml = convertTextToHtml(extractedText)

      // Apply the same formatting enhancements to RTF
      extractedHtml = enhanceDocumentFormatting(basicHtml)

      console.log('RTF extraction completed')
      console.log('Extracted text length:', extractedText.length)
    }

    if (!extractedText.trim()) {
      throw new Error(`No text content could be extracted from the ${fileType.toUpperCase()} file`)
    }

    // Auto-detect chapters from the HTML structure
    onProgress?.(70, 'Detecting chapters and structure...')
    const chapters = await detectChaptersFromHtml(extractedHtml, extractedText, previewOnly)

    if (chapters.length === 0) {
      // If no chapters detected, create a single chapter with all content
      const { WordTokenizer } = await import('natural')
      const wordTokenizer = new WordTokenizer()
      const words = wordTokenizer.tokenize(extractedText.toLowerCase()) || []

      chapters.push({
        title: 'Chapter 1',
        content: extractedHtml,
        chapter_number: 1,
        word_count: words.length
      })
    }

    // Calculate totals
    onProgress?.(90, 'Calculating word counts and metadata...')
    const totalWordCount = chapters.reduce((sum, ch) => sum + ch.word_count, 0)
    const readingTime = await import('reading-time')
    const readingTimeResult = readingTime.default(extractedText)
    const pageCount = Math.ceil(totalWordCount / 250)

    onProgress?.(100, 'Document processing complete!')
    console.log(`Document processing complete: ${chapters.length} chapters, ${totalWordCount} words`)

    return {
      chapters,
      wordCount: totalWordCount,
      readingTimeMinutes: Math.round(readingTimeResult.minutes), // Round to integer
      pageCount,
      metadata: {
        title: 'Untitled Document',
        author: 'Unknown Author',
        description: ''
      }
    }

  } catch (error) {
    console.error(`${fileType.toUpperCase()} processing error:`, error)
    throw new Error(`${fileType.toUpperCase()} processing failed: ${error.message}`)
  }
}

/**
 * Enhance document formatting for better e-reader display
 */
function enhanceDocumentFormatting(html: string): string {
  console.log('Enhancing document formatting...')

  // Step 1: Clean up and normalize HTML
  let enhanced = html
    // Remove empty paragraphs
    .replace(/<p>\s*<\/p>/g, '')
    // Remove excessive whitespace
    .replace(/\s+/g, ' ')
    // Fix paragraph spacing
    .replace(/<\/p>\s*<p>/g, '</p>\n<p>')

  // Step 2: Detect and format chapter headers
  enhanced = detectAndFormatChapterHeaders(enhanced)

  // Step 3: Improve paragraph formatting
  enhanced = improveParagraphFormatting(enhanced)

  // Step 4: Handle special formatting
  enhanced = handleSpecialFormatting(enhanced)

  console.log('Document formatting enhanced')
  return enhanced
}

/**
 * Detect and format chapter headers using consistent pattern detection
 */
function detectAndFormatChapterHeaders(html: string): string {
  console.log('Analyzing document structure...')

  // First, check if the document already has traditional chapter headings
  const traditionalChapterRegex = /<p>(Chapter\s+(?:\d+|One|Two|Three|Four|Five|Six|Seven|Eight|Nine|Ten|[IVX]+).*?)<\/p>/gi
  const traditionalChapters = html.match(traditionalChapterRegex) || []

  if (traditionalChapters.length > 0) {
    console.log(`Found ${traditionalChapters.length} traditional chapter headings - using Chapter pattern only`)

    // Use ONLY traditional chapter patterns
    html = html.replace(traditionalChapterRegex, (match, content) => {
      console.log('Found traditional chapter:', content)
      return `<h2>${content}</h2>`
    })

    return html
  }

  // If no traditional chapters found, look for POV headers
  console.log('No traditional chapters found - checking for POV headers...')

  // Count potential POV headers first
  const povHeaderRegex = /<p><strong>([A-Z]+)<\/strong><\/p>/g
  const potentialPOVHeaders = []
  let match

  while ((match = povHeaderRegex.exec(html)) !== null) {
    const text = match[1].trim()

    // Very strict criteria for POV headers:
    const isPOVHeader = (
      text.length >= 3 && text.length <= 15 && // Character names are typically 3-15 characters
      text.match(/^[A-Z]+$/) && // Must be ALL CAPS
      !text.includes(' ') && // Single word only (no spaces)
      !text.match(/^(THE|AND|OR|BUT|FOR|WITH|TO|FROM|BY|IN|ON|AT|YES|NO|OK|WHY|HOW|WHAT|WHEN|WHERE|WHO)$/) // Exclude common words
    )

    if (isPOVHeader) {
      potentialPOVHeaders.push(text)
    }
  }

  if (potentialPOVHeaders.length > 0) {
    console.log(`Found ${potentialPOVHeaders.length} POV headers - using POV pattern only`)

    // Use ONLY POV headers
    html = html.replace(/<p><strong>([A-Z]+)<\/strong><\/p>/g, (match, content) => {
      const text = content.trim()

      const isPOVHeader = (
        text.length >= 3 && text.length <= 15 &&
        text.match(/^[A-Z]+$/) &&
        !text.includes(' ') &&
        !text.match(/^(THE|AND|OR|BUT|FOR|WITH|TO|FROM|BY|IN|ON|AT|YES|NO|OK|WHY|HOW|WHAT|WHEN|WHERE|WHO)$/)
      )

      if (isPOVHeader) {
        console.log('Found POV header:', text)
        return `<h2>${text}</h2>`
      }
      return match
    })

    return html
  }

  console.log('No consistent chapter pattern found - document may not have clear chapter structure')
  return html
}

/**
 * Improve paragraph formatting for better readability
 */
function improveParagraphFormatting(html: string): string {
  console.log('Improving paragraph formatting...')

  // Add proper spacing between paragraphs
  html = html.replace(/<\/p>\s*<p>/g, '</p>\n\n<p>')

  // Handle dialogue formatting (quotes) - keep simple
  html = html.replace(/<p>"([^"]*)"<\/p>/g, '<p style="margin-left: 2em; text-indent: 0;">"$1"</p>')

  // Handle emphasis and italics - keep them simple without extra classes
  // Don't add classes that could break rendering

  // Center short lines that might be scene breaks or titles
  html = html.replace(/<p>([^<]{1,30})<\/p>/g, (match, content) => {
    const text = content.trim()
    if (text.match(/^[\*\-\~\=\s]+$/) || text.match(/^\* \* \*$/)) {
      return `<p style="text-align: center; margin: 2em 0;">${text}</p>`
    }
    return match
  })

  return html
}

/**
 * Handle special formatting like scene breaks, emphasis, etc.
 */
function handleSpecialFormatting(html: string): string {
  console.log('Handling special formatting...')

  // Scene breaks (*, ---, etc.) - keep simple
  html = html.replace(/<p>(\*\s*\*\s*\*|---+|\~\~\~+)<\/p>/g, '<p style="text-align: center; margin: 2em 0;">$1</p>')

  // Handle centered text - keep simple
  html = html.replace(/<p style="text-align: center">([^<]+)<\/p>/g, '<p style="text-align: center;">$1</p>')

  // Keep HTML simple - no complex CSS wrappers that could break rendering

  return html
}

/**
 * Extract text from RTF document structure
 */
function extractTextFromRtfDoc(doc: any): string {
  if (!doc || !doc.content) return ''

  function extractFromContent(content: any[]): string {
    return content.map(item => {
      if (typeof item === 'string') {
        return item
      } else if (item && item.content) {
        return extractFromContent(item.content)
      }
      return ''
    }).join('')
  }

  return extractFromContent(doc.content).trim()
}

/**
 * Convert plain text to basic HTML
 */
function convertTextToHtml(text: string): string {
  return text
    .split('\n\n')
    .map(paragraph => paragraph.trim())
    .filter(paragraph => paragraph.length > 0)
    .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
    .join('\n')
}

/**
 * Detect chapters from enhanced HTML content
 */
async function detectChaptersFromHtml(html: string, plainText: string, previewOnly: boolean = false): Promise<Chapter[]> {
  const { WordTokenizer } = await import('natural')
  const wordTokenizer = new WordTokenizer()

  // Look for our enhanced chapter headers first
  const chapterHeaderRegex = /<h2[^>]*>(.*?)<\/h2>/gi
  const headings: Array<{ title: string; position: number }> = []

  let match
  while ((match = chapterHeaderRegex.exec(html)) !== null) {
    const title = match[1].replace(/<[^>]*>/g, '').trim()
    if (title.length > 0) {
      headings.push({
        title,
        position: match.index
      })
    }
  }

  // Fallback: Look for standard heading patterns
  if (headings.length === 0) {
    const standardHeadingRegex = /<h[1-3][^>]*>(.*?)<\/h[1-3]>/gi
    while ((match = standardHeadingRegex.exec(html)) !== null) {
      const title = match[1].replace(/<[^>]*>/g, '').trim()
      if (title.length > 0 && title.length <= 100) {
        headings.push({
          title,
          position: match.index
        })
      }
    }
  }

  const chapters: Chapter[] = []

  console.log(`Found ${headings.length} potential chapters`)

  if (headings.length === 0) {
    console.log('No chapters detected, will create single chapter')
    return []
  }

  // Create chapters based on detected headings
  for (let i = 0; i < headings.length; i++) {
    if (previewOnly && i > 0) break // Only first chapter for preview

    const heading = headings[i]
    const nextHeading = headings[i + 1]

    const startPos = heading.position
    const endPos = nextHeading ? nextHeading.position : html.length

    let chapterHtml = html.substring(startPos, endPos).trim()

    // Ensure we don't cut HTML tags in the middle - find safe boundaries
    if (nextHeading && chapterHtml.length > 0) {
      // Look for the last complete tag before the next chapter
      const lastCompleteTagEnd = chapterHtml.lastIndexOf('>')
      const lastIncompleteTagStart = chapterHtml.lastIndexOf('<')

      // If there's an incomplete tag at the end, trim it
      if (lastIncompleteTagStart > lastCompleteTagEnd) {
        chapterHtml = chapterHtml.substring(0, lastIncompleteTagStart).trim()
      }
    }

    // Clean up chapter content
    chapterHtml = cleanChapterContent(chapterHtml)

    const chapterText = chapterHtml.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()

    if (chapterText.length > 50) { // Only include substantial chapters
      const words = wordTokenizer.tokenize(chapterText.toLowerCase()) || []

      console.log(`Creating chapter ${chapters.length + 1}: "${heading.title}" (${words.length} words)`)

      chapters.push({
        title: heading.title,
        content: chapterHtml,
        chapter_number: chapters.length + 1,
        word_count: words.length
      })
    }
  }

  console.log(`Successfully created ${chapters.length} chapters`)
  return chapters
}

/**
 * Clean up chapter content for better display - convert HTML to clean text
 */
function cleanChapterContent(html: string): string {
  // Remove duplicate chapter headers (since we'll add our own)
  html = html.replace(/<h2[^>]*>.*?<\/h2>/gi, '')

  // Fix broken HTML tags at the beginning of content (common at chapter boundaries)
  // Look for incomplete opening tags at the start
  html = html.replace(/^[^<]*<([a-zA-Z]+)[^>]*>/, (match, tagName) => {
    // If we find an incomplete tag at the start, remove the broken part
    const brokenTagStart = match.indexOf('<')
    if (brokenTagStart > 0) {
      // Remove everything before the tag and the broken tag itself
      return ''
    }
    return match
  })

  // Fix broken closing tags or incomplete tags at the beginning
  html = html.replace(/^[^<]*<\/[a-zA-Z]+>/, '')
  html = html.replace(/^[^<]*class="[^"]*"[^>]*>/, '')
  html = html.replace(/^[^<]*em class="[^"]*">/, '')

  // Convert HTML to clean text for e-reader
  let cleanText = html
    // Convert paragraphs to double line breaks
    .replace(/<\/p>\s*<p>/g, '\n\n')
    .replace(/<p[^>]*>/g, '')
    .replace(/<\/p>/g, '\n\n')

    // Convert emphasis tags to markdown-style formatting
    .replace(/<em[^>]*>(.*?)<\/em>/g, '*$1*')
    .replace(/<i[^>]*>(.*?)<\/i>/g, '*$1*')
    .replace(/<strong[^>]*>(.*?)<\/strong>/g, '**$1**')
    .replace(/<b[^>]*>(.*?)<\/b>/g, '**$1**')

    // Convert headings to markdown-style
    .replace(/<h1[^>]*>(.*?)<\/h1>/g, '# $1\n\n')
    .replace(/<h2[^>]*>(.*?)<\/h2>/g, '## $1\n\n')
    .replace(/<h3[^>]*>(.*?)<\/h3>/g, '### $1\n\n')
    .replace(/<h4[^>]*>(.*?)<\/h4>/g, '#### $1\n\n')
    .replace(/<h5[^>]*>(.*?)<\/h5>/g, '##### $1\n\n')
    .replace(/<h6[^>]*>(.*?)<\/h6>/g, '###### $1\n\n')

    // Convert line breaks
    .replace(/<br\s*\/?>/g, '\n')

    // Remove any remaining HTML tags (including broken ones)
    .replace(/<[^>]*>/g, '')

    // Clean up any remaining broken HTML artifacts
    .replace(/class="[^"]*"/g, '')
    .replace(/style="[^"]*"/g, '')
    .replace(/[a-zA-Z]+="[^"]*"/g, '')

    // Clean up whitespace
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Multiple line breaks to double
    .replace(/^\s+|\s+$/g, '') // Trim start/end
    .replace(/[ \t]+/g, ' ') // Multiple spaces to single

  return cleanText
}

export { processEPUB, processDocumentToEPUB }
