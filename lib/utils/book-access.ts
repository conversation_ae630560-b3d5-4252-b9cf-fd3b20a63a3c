import { SupabaseClient } from '@supabase/supabase-js'

export interface BookAccessResult {
  hasAccess: boolean
  accessType: 'free' | 'purchased' | 'library' | 'none'
  book?: {
    id: string
    title: string
    price_amount: number
    user_id: string
  }
}

/**
 * Check if a user has access to a book through multiple pathways:
 * 1. Book is free
 * 2. User has purchased the book
 * 3. Book is in user's library
 */
export async function checkBookAccess(
  supabase: SupabaseClient,
  userId: string,
  bookId: string
): Promise<BookAccessResult> {
  try {
    console.log('checkBookAccess: Checking access for user:', userId, 'book:', bookId)

    // First, get the book details
    const { data: book, error: bookError } = await supabase
      .from('projects')
      .select('id, title, price_amount, user_id')
      .eq('id', bookId)
      .eq('is_ebook', true)
      .single()

    console.log('checkBookAccess: Book query result:', { book, bookError })

    if (bookError || !book) {
      console.log('checkBookAccess: Book not found or error')
      return { hasAccess: false, accessType: 'none' }
    }

    // Check if user is the author of the book
    if (book.user_id === userId) {
      console.log('checkBookAccess: User is the author, granting access')
      return {
        hasAccess: true,
        accessType: 'free', // Authors get free access to their own books
        book
      }
    }

    // Check if book is free
    if (book.price_amount === 0) {
      console.log('checkBookAccess: Book is free, granting access')
      return {
        hasAccess: true,
        accessType: 'free',
        book
      }
    }

    // Check if user has purchased this book
    console.log('checkBookAccess: Checking purchase status')
    const { data: purchaseData, error: purchaseError } = await supabase
      .from('book_purchases')
      .select('id')
      .eq('user_id', userId)
      .eq('project_id', bookId)
      .single()

    console.log('checkBookAccess: Purchase query result:', { purchaseData, purchaseError })

    if (purchaseData) {
      console.log('checkBookAccess: User has purchased book')
      return {
        hasAccess: true,
        accessType: 'purchased',
        book
      }
    }

    // Check if book is in user's library
    console.log('checkBookAccess: Checking library status')
    const { data: libraryData, error: libraryError } = await supabase
      .from('user_library')
      .select('access_type')
      .eq('user_id', userId)
      .eq('project_id', bookId)
      .single()

    console.log('checkBookAccess: Library query result:', { libraryData, libraryError })

    if (libraryData) {
      console.log('checkBookAccess: Book found in user library')
      return {
        hasAccess: true,
        accessType: 'library',
        book
      }
    }

    // No access found
    console.log('checkBookAccess: No access found')
    return {
      hasAccess: false,
      accessType: 'none',
      book
    }

  } catch (error) {
    console.error('Error checking book access:', error)
    return { hasAccess: false, accessType: 'none' }
  }
}

/**
 * Check if a user has access to read a book (stricter check for the read page)
 */
export async function checkBookReadAccess(
  supabase: SupabaseClient,
  userId: string,
  bookId: string
): Promise<BookAccessResult> {
  const result = await checkBookAccess(supabase, userId, bookId)
  
  // For reading, we need either free access, purchase, or library entry
  if (result.hasAccess && ['free', 'purchased', 'library'].includes(result.accessType)) {
    return result
  }

  return { hasAccess: false, accessType: 'none', book: result.book }
}
